import pygame as py
import random

screen_height = 1000
screen_width = 1000
screencolor = (255, 255, 255)
rectcolor = (255, 0, 0)
rectcolor2 = (0, 255, 0)

py.init()
screen = py.display.set_mode((screen_height, screen_width))
screen.fill(screencolor)
py.display.set_caption("AI test")
# Class ENTITY start
class Entity:
    def __init__(self, x, y, size, color, speed):
        self.x = x
        self.y = y
        self.rect = py.Rect(self.x, self.y, size, size)
        self.color = color
        self.speed = speed

    def draw(self, surface):
        py.draw.rect(surface, self.color, self.rect)

class Target:
    def __init__(self, x, y, size, color):
        self.pos = py.Vector2(x, y)
        self.size = size
        self.color = color
        self.direction = py.Vector2(random.choice([-1,1]), random.choice([-1,1]))
        self.speed = 0.5

    def move(self):
        self.pos += self.direction * self.speed
        if self.pos.x < self.size or self.pos.x > screen_height - self.size:
            self.direction.x *= -1
        if self.pos.y < self.size or self.pos.y > screen_width - self.size:
            self.direction.y *= -1

    def draw(self, surface):
        py.draw.circle(surface, self.color, (int(self.pos.x), int(self.pos.y)), self.size)

    def get_rect(self):
        return py.Rect(self.pos.x - self.size, self.pos.y - self.size, self.size*2, self.size*2)
# Class ENTITY end
def random_position(size):
    x = random.randint(size, screen_width - size)
    y = random.randint(size, screen_height - size)
    return x, y
# Function MAIN start
def main():
    running = True
    player = Entity(150, 150, 20, rectcolor, 30)
    enemy = Entity(100, 100, 20, rectcolor2, 20)
    target_pos = random_position(15)
    target = Target(*target_pos, 15, (255, 255, 0))
    while running:
        screen.fill(screencolor)
        player.draw(screen)
        target.move()
        direction_to_target = py.Vector2(target.pos.x - enemy.rect.centerx, target.pos.y - enemy.rect.centery)
        if direction_to_target.length() != 0:
            direction_to_target = direction_to_target
        enemy.rect.centerx += direction_to_target.x * enemy.speed
        enemy.rect.centery += direction_to_target.y * enemy.speed

        enemy.draw(screen)

        target.draw(screen)
        for event in py.event.get():
            if event.type == py.QUIT:
                running = False
            if event.type == py.KEYDOWN:
                if event.key == py.K_w:
                    player.y -= player.speed
                elif event.key == py.K_s:
                    player.y += player.speed
                elif event.key == py.K_a:
                    player.x -= player.speed
                elif event.key == py.K_d:
                    player.x += player.speed
                player.rect.topleft = (player.x, player.y)
        py.display.flip()



if __name__ == "__main__":
    main()